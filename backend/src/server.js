// Global error handlers - must be at the very top
process.on('uncaughtException', (error) => {
  console.error('\n=== UNCAUGHT EXCEPTION ===');
  console.error('A critical error occurred that was not caught!');
  console.error('Error:', error);
  console.error('Stack:', error.stack);

  // Attempt a graceful shutdown
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('\n=== UNHANDLED REJECTION ===');
  console.error('A promise rejection was not handled!');
  console.error('Reason:', reason);

  // In development, we might want to crash the app to be notified
  if (process.env.NODE_ENV === 'development') {
    process.exit(1);
  }
});

// Load environment variables and require dependencies
require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const http = require('http');
const { Server } = require('socket.io');
const cookieParser = require('cookie-parser');
// const mongoSanitize = require('express-mongo-sanitize'); // Removed due to Express 5 compatibility issues
// const xss = require('xss-clean'); // Removed due to Express 5 compatibility issues
const hpp = require('hpp');
const rateLimit = require('express-rate-limit');

// Import local modules
const logger = require('./config/logger');
const { connectRedis } = require('./config/redis');
const { initSentry, sentryRequestHandler, sentryTracingHandler, sentryErrorHandler } = require('./config/sentry');
const { initializeMetrics, collectHttpMetrics } = require('./middleware/metrics');

// Initialize monitoring and metrics with error handling
let metricsEnabled = false;
let register, updateBusinessMetrics;

try {
  const metrics = require('./middleware/metrics');
  register = metrics.register;
  // collectHttpMetrics is already imported above, no need to redeclare it
  updateBusinessMetrics = metrics.updateBusinessMetrics;
  metricsEnabled = true;
  logger.info('Metrics middleware enabled successfully');
} catch (error) {
  logger.warn('Failed to enable metrics middleware, continuing without metrics:', error.message);
}
// Import security middleware
const {
  rateLimiters,
  helmetConfig,
  corsOptions,
  securityHeaders,
  sanitizeInput,
  requestLogger,
  rateLimit: securityRateLimit
} = require('./middleware/security');

const app = express();

// Initialize Sentry
initSentry(app);

// Apply CORS before other middleware
app.use(cors(corsOptions));

// Initialize Redis only if explicitly configured
if (process.env.REDIS_URL || (process.env.REDIS_HOST && process.env.REDIS_PORT)) {
  try {
    const redisClient = connectRedis();
    if (!redisClient) {
      logger.warn('Redis is not available. Running without Redis.');
    }
  } catch (error) {
    logger.warn('Redis initialization failed, continuing without Redis:', error.message);
  }
} else {
  logger.info('Redis not configured. Running without Redis.');
}

// Connect to MongoDB
const { connectDB } = require('./config/database');
connectDB().catch(err => {
  logger.error('Failed to connect to MongoDB:', err);
  process.exit(1);
});

// Socket.io initialization temporarily disabled due to compatibility issues
/*
const httpServer = http.createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.NODE_ENV === 'production'
      ? process.env.CLIENT_URL
      : ['http://localhost:5173', 'http://127.0.0.1:5173'],
    methods: ['GET', 'POST'],
    credentials: true
  },
});

io.on('connection', (socket) => {
  logger.info('A user connected');
  // Join a room for farm-specific notifications
  socket.on('joinFarmRoom', (farmId) => {
    socket.join(farmId);
    logger.info(`User joined farm room: ${farmId}`);
  });

  socket.on('disconnect', () => {
    logger.info('User disconnected');
  });
});

// Make io accessible to routes
app.set('socketio', io);
*/

// Create a basic HTTP server without socket.io
const httpServer = http.createServer(app);
logger.warn('Socket.io is currently disabled due to compatibility issues');

// Sentry request handler (must be first)
app.use(sentryRequestHandler());
app.use(sentryTracingHandler());

// Apply security middleware
app.use(helmet(helmetConfig));
app.use(express.json({ limit: '10mb' })); // Body parser for JSON
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());
app.use(cors(corsOptions)); // Enable CORS with security options

// Security headers
app.use(securityHeaders);

// Request logging
app.use(requestLogger);

// Input sanitization (custom implementation to avoid Express 5 compatibility issues)
app.use((req, res, next) => {
  // Only sanitize body to avoid query modification issues
  if (req.body && typeof req.body === 'object') {
    // Remove MongoDB operators and XSS patterns
    const sanitized = JSON.stringify(req.body)
      .replace(/\$[a-zA-Z_][a-zA-Z0-9_]*/g, '') // Remove MongoDB operators
      .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, ''); // Remove event handlers

    try {
      req.body = JSON.parse(sanitized);
    } catch (e) {
      req.body = {};
    }
  }
  next();
});
// app.use(xss()); // Removed due to Express 5 compatibility issues
app.use(hpp());

// Rate Limiting
app.use(rateLimiters.general);

// Database Connection
const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/smartpoultry';
mongoose.connect(mongoURI)
  .then(() => {
    logger.info('MongoDB connected successfully!');
    // Initialize business metrics after DB connection - temporarily disabled
    // updateBusinessMetrics();
  })
  .catch(err => {
    logger.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Basic Route
const authRoutes = require('./routes/authRoutes');
const farmRoutes = require('./routes/farmRoutes');
const chickenRoutes = require('./routes/chickenRoutes');
const eggProductionRoutes = require('./routes/eggProductionRoutes');
const feedRecordRoutes = require('./routes/feedRecordRoutes');
const healthRecordRoutes = require('./routes/healthRecordRoutes');
const financialRecordRoutes = require('./routes/financialRecordRoutes');
const supplierRoutes = require('./routes/supplierRoutes');
const customerRoutes = require('./routes/customerRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const aiInsightLogRoutes = require('./routes/aiInsightLogRoutes');
const dashboardRoutes = require('./routes/dashboardRoutes');
const userRoutes = require('./routes/userRoutes');

// Routes with specific rate limiting
app.use('/api/v1/auth', rateLimiters.auth, authRoutes);
app.use('/api/v1/farms', farmRoutes);
app.use('/api/v1/farms/:farmId/chickens', chickenRoutes);
app.use('/api/v1/farms/:farmId/egg-production', eggProductionRoutes);
app.use('/api/v1/farms/:farmId/feed-records', feedRecordRoutes);
app.use('/api/v1/farms/:farmId/health-records', healthRecordRoutes);
app.use('/api/v1/farms/:farmId/financial-records', financialRecordRoutes);
app.use('/api/v1/farms/:farmId/suppliers', supplierRoutes);
app.use('/api/v1/farms/:farmId/customers', customerRoutes);
app.use('/api/v1/farms/:farmId/analytics', analyticsRoutes);
app.use('/api/v1/farms/:farmId/notifications', notificationRoutes);
app.use('/api/v1/farms/:farmId/dashboard', dashboardRoutes);
app.use('/api/v1/farms/:farmId/ai-insights', aiInsightLogRoutes);
app.use('/api/v1/users', userRoutes);

app.get('/', (req, res) => {
  res.send('SmartPoultry Backend API is running!');
});

// Health check endpoint
app.get('/health', (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: Date.now(),
    database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    environment: process.env.NODE_ENV || 'development'
  };

  try {
    res.status(200).json(healthCheck);
  } catch (error) {
    healthCheck.message = error;
    res.status(503).json(healthCheck);
  }
});

// Metrics endpoint for Prometheus
app.get('/metrics', async (req, res) => {
  if (!metricsEnabled) {
    return res.status(503).json({
      status: 'error',
      message: 'Metrics are not enabled on this server'
    });
  }

  try {
    res.set('Content-Type', register.contentType);
    const metrics = await register.metrics();
    res.end(metrics);
  } catch (error) {
    logger.error('Error generating metrics:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate metrics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Sentry error handler (must be before other error handlers)
app.use(sentryErrorHandler());

// Error Handling Middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);

  // Log the full error including stack trace
  console.error('Unhandled error:', err);
  if (err.stack) {
    console.error('Stack trace:', err.stack);
  }

  // Don't leak error details in production
  const message = process.env.NODE_ENV === 'production'
    ? 'Internal server error'
    : err.message;

  res.status(err.status || 500).json({
    message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// Log unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Log to file as well
  logger.error('Unhandled Rejection at:', { promise, reason });
});

// Log uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  if (error.stack) {
    console.error('Stack trace:', error.stack);
  }
  // Log to file as well
  logger.error('Uncaught Exception:', error);
  // Exit the process to prevent the application from running in an undefined state
  process.exit(1);
});

// Use port 5001 to avoid conflicts with other services
const PORT = process.env.PORT || 5001;

// Add error handling for server startup
httpServer.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    logger.error(`Port ${PORT} is already in use. Please stop the other process or use a different port.`);
  } else {
    logger.error('Server error:', error);
  }
  process.exit(1);
});

// Start the server with detailed logging
console.log(`\n=== Starting SmartPoultry Backend ===`);
console.log(`Timestamp: ${new Date().toISOString()}`);
console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`Node.js: ${process.version}`);
console.log(`Platform: ${process.platform} ${process.arch}`);
console.log(`PID: ${process.pid}`);
console.log(`Current directory: ${process.cwd()}`);
console.log(`Memory usage: ${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(2)} MB`);

// Log important environment variables (mask sensitive ones)
console.log('\n=== Environment Variables ===');
const envVars = ['NODE_ENV', 'PORT', 'MONGO_URI', 'JWT_SECRET', 'REDIS_URL'];
envVars.forEach(key => {
  const value = process.env[key] || '(not set)';
  const maskedValue = ['JWT_SECRET', 'REDIS_PASSWORD'].includes(key)
    ? '***'
    : value;
  console.log(`${key}=${maskedValue}`);
});

// Log all loaded modules
console.log('\n=== Loaded Modules ===');
console.log('Express version:', require('express/package.json').version);
console.log('Mongoose version:', require('mongoose/package.json').version);
// Temporarily disabled Socket.IO version logging due to compatibility issues
// console.log('Socket.IO version:', require('socket.io/package.json').version);

// Log middleware stack
console.log('\n=== Middleware Stack ===');
console.log('1. Request parsing (JSON, URL-encoded)');
console.log('2. Cookie parsing');
console.log('3. CORS');
console.log('4. Security headers');
console.log('5. Request logging');
console.log('6. Rate limiting');
console.log('7. Input sanitization');
console.log('8. Metrics collection');
console.log('9. API routes');
console.log('10. Error handling');

// Log routes
console.log('\n=== Available Routes ===');
console.log('GET    /health           - Health check endpoint');
console.log('GET    /metrics          - Prometheus metrics');
console.log('GET    /api/v1/...       - API v1 endpoints');
console.log('WS     /socket.io/...    - WebSocket connections');

// Start the HTTP server
console.log(`\n=== Starting HTTP Server ===`);
console.log(`Attempting to listen on port ${PORT}...`);

// Add server error handler
httpServer.on('error', (error) => {
  console.error('\n=== Server Error ===');
  console.error('Error starting server:', error);

  if (error.code === 'EADDRINUSE') {
    console.error(`\nPort ${PORT} is already in use by another process.`);
    console.error('Please stop the other process or use a different port.');
    console.log('\nTo find and stop the process using port 5001, run:');
    console.log('  lsof -i :5001');
    console.log('  kill -9 <PID>');
  } else if (error.code === 'EACCES') {
    console.error(`\nPermission denied when trying to use port ${PORT}.`);
    console.error('Try using a port number higher than 1024.');
  }

  process.exit(1);
});

// Start listening
httpServer.listen(PORT, '0.0.0.0', () => {
  const address = httpServer.address();
  const host = address.address === '::' ? 'localhost' : address.address;
  const port = address.port;
  const protocol = 'http';
  const localUrl = `${protocol}://${host}:${port}`;

  console.log('\n=== Server Started Successfully ===');
  console.log(`Server is running at:`);
  console.log(`- Local:   ${localUrl}`);
  console.log(`- Network: ${protocol}://${require('os').hostname()}:${port}`);
  console.log(`\nAvailable endpoints:`);
  console.log(`- Health check:  ${localUrl}/health`);
  console.log(`- Metrics:       ${localUrl}/metrics`);
  console.log(`- API Docs:      ${localUrl}/api-docs`);
  console.log(`\nPress Ctrl+C to stop the server`);

  // Log to the application log as well
  logger.info(`Server started successfully on port ${port}`, {
    environment: process.env.NODE_ENV || 'development',
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    memoryUsage: process.memoryUsage(),
    uptime: process.uptime()
  });
});

// Handle process termination
process.on('SIGINT', () => {
  logger.info('SIGINT received. Shutting down gracefully...');
  httpServer.close(() => {
    logger.info('Server closed');
    mongoose.connection.close(false, () => {
      logger.info('MongoDB connection closed');
      process.exit(0);
    });
  });
});




