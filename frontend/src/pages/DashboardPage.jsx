import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useFarm } from '../contexts/FarmContext';
import DashboardLayout from '../components/DashboardLayout';
import FarmSelector from '../components/FarmSelector';
import SummaryCards from '../components/SummaryCards';
import EggProductionChart from '../components/EggProductionChart';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import axios from 'axios';

const DashboardPage = () => {
  const { t } = useTranslation();
  const { user, token } = useAuth();
  const { selectedFarm } = useFarm();
  const [summaryData, setSummaryData] = useState({});
  const [eggChartData, setEggChartData] = useState([]);
  const [dailySummaryAI, setDailySummaryAI] = useState('');
  const [eggForecastAI, setEggForecastAI] = useState('');
  const [loadingData, setLoadingData] = useState(true);
  const [errorData, setErrorData] = useState(null);
  const [notificationTriggerStatus, setNotificationTriggerStatus] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!selectedFarm || !token) {
        setLoadingData(false);
        return;
      }

      setLoadingData(true);
      setErrorData(null);

      try {
        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };

        // Fetch Summary Data
        const summaryRes = await axios.get(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/dashboard/summary`, config);
        setSummaryData(summaryRes.data);

        // Fetch Egg Production Chart Data
        const eggChartRes = await axios.get(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/dashboard/egg-production-chart-data`, config);
        setEggChartData(eggChartRes.data);

        // Fetch AI Daily Summary
        const dailySummaryAIRes = await axios.get(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/analytics/daily-summary`, config);
        setDailySummaryAI(dailySummaryAIRes.data.summary);

        // Fetch AI Egg Forecast
        const eggForecastAIRes = await axios.get(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/analytics/egg-forecast`, config);
        setEggForecastAI(eggForecastAIRes.data.forecast);

      } catch (err) {
        console.error('Error fetching dashboard data:', err.response ? err.response.data : err.message);
        setErrorData(err.response?.data?.message || t('failed_to_fetch_dashboard_data'));
      } finally {
        setLoadingData(false);
      }
    };

    fetchDashboardData();
  }, [selectedFarm, token]);

  const handleTriggerLowFeedAlert = async () => {
    if (!selectedFarm || !token) {
      setNotificationTriggerStatus(t('select_farm_first'));
      return;
    }

    try {
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
      await axios.post(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/notifications/low-feed-alert`, { message: t('test_low_feed_alert_message') }, config);
      setNotificationTriggerStatus(t('alert_triggered_successfully'));
    } catch (err) {
      console.error('Error triggering alert:', err.response ? err.response.data : err.message);
      setNotificationTriggerStatus(err.response?.data?.message || t('failed_to_trigger_alert'));
    }
  };

  if (!user) {
    return <div className="min-h-screen bg-gray-100 flex items-center justify-center text-red-500 text-xl">{t('not_authorized')}</div>;
  }

  return (
    <DashboardLayout>
      <div className="mb-8">
        <h2 className="text-4xl font-bold text-neutral-900 font-display mb-2">{t('dashboard')}</h2>
        <p className="text-lg text-neutral-600">Manage your poultry farm with AI-powered insights</p>
      </div>

      <FarmSelector />

      {selectedFarm ? (
        <div className="mt-8">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{selectedFarm.name}</h3>
                    <p className="text-sm text-neutral-600">{selectedFarm.location}</p>
                  </div>
                </div>
                <Badge variant="success" size="md">Active Farm</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <Button
                  variant="warning"
                  size="sm"
                  onClick={handleTriggerLowFeedAlert}
                >
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {t('trigger_test_low_feed_alert')}
                </Button>
                {notificationTriggerStatus && (
                  <Badge variant="info" size="sm">{notificationTriggerStatus}</Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {loadingData ? (
            <p>{t('loading')}...</p>
          ) : errorData ? (
            <p className="text-red-500">{errorData}</p>
          ) : (
            <>
              <SummaryCards data={summaryData} />
              <EggProductionChart data={eggChartData} />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                <Card className="hover:shadow-large transition-all duration-300">
                  <CardHeader className="bg-gradient-to-r from-primary-50 to-primary-100">
                    <CardTitle className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm font-bold">AI</span>
                      </div>
                      <span>{t('daily_summary')}</span>
                      <Badge variant="success" size="sm">Live</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="prose prose-sm max-w-none">
                      <p className="text-neutral-700 leading-relaxed">{dailySummaryAI}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-large transition-all duration-300">
                  <CardHeader className="bg-gradient-to-r from-accent-50 to-accent-100">
                    <CardTitle className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-accent-500 to-accent-600 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                      </div>
                      <span>{t('egg_forecast')}</span>
                      <Badge variant="info" size="sm">7 Days</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="prose prose-sm max-w-none">
                      <p className="text-neutral-700 leading-relaxed">{eggForecastAI}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </div>
      ) : (
        <p className="mt-8 text-gray-600">{t('no_farm_selected')}</p>
      )}
    </DashboardLayout>
  );
};

export default DashboardPage;
