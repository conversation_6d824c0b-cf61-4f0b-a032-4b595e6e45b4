import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useFarm } from '../contexts/FarmContext';
import DashboardLayout from '../components/DashboardLayout';
import ChickenList from '../components/ChickenList';
import ChickenForm from '../components/ChickenForm';
import { Card, CardContent } from '../components/ui/Card';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import Modal from '../components/ui/Modal';
import { SkeletonCard } from '../components/ui/Skeleton';
import axios from 'axios';

const ChickensPage = () => {
  const { t } = useTranslation();
  const { token, user } = useAuth();
  const { selectedFarm } = useFarm();
  const [chickens, setChickens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingChicken, setEditingChicken] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const canManage = user && (user.roles.includes('admin') || user.roles.includes('manager'));

  useEffect(() => {
    const fetchChickens = async () => {
      if (!selectedFarm || !token) {
        setLoading(false);
        return;
      }
      try {
        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        const res = await axios.get(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens`, config);
        setChickens(res.data);
      } catch (err) {
        console.error('Error fetching chickens:', err.response ? err.response.data : err.message);
        setError(err.response?.data?.message || t('failed_to_fetch_chickens'));
      } finally {
        setLoading(false);
      }
    };

    fetchChickens();
  }, [selectedFarm, token]);

  const handleAddChicken = async (chickenData) => {
    try {
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
      const res = await axios.post(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens`, chickenData, config);
      setChickens([...chickens, res.data.chicken]);
      setEditingChicken(null); // Clear form after adding
    } catch (err) {
      console.error('Error adding chicken:', err.response ? err.response.data : err.message);
      setError(err.response?.data?.message || t('failed_to_save_chicken'));
    }
  };

  const handleUpdateChicken = async (id, chickenData) => {
    try {
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
      const res = await axios.put(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens/${id}`, chickenData, config);
      setChickens(chickens.map((chicken) => (chicken._id === id ? res.data.chicken : chicken)));
      setEditingChicken(null); // Clear form after updating
    } catch (err) {
      console.error('Error updating chicken:', err.response ? err.response.data : err.message);
      setError(err.response?.data?.message || t('failed_to_save_chicken'));
    }
  };

  const handleDeleteChicken = async (id) => {
    if (window.confirm(t('confirm_delete_chicken'))) {
      try {
        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        await axios.delete(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens/${id}`, config);
        setChickens(chickens.filter((chicken) => chicken._id !== id));
      } catch (err) {
        console.error('Error deleting chicken:', err.response ? err.response.data : err.message);
        setError(err.response?.data?.message || t('failed_to_delete_chicken'));
      }
    }
  };

  // Filter chickens based on search and status
  const filteredChickens = chickens.filter(chicken => {
    const matchesSearch = chicken.breed?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         chicken.notes?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || chicken.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const statusCounts = chickens.reduce((acc, chicken) => {
    acc[chicken.status] = (acc[chicken.status] || 0) + 1;
    return acc;
  }, {});

  if (!selectedFarm) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-neutral-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-neutral-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-neutral-900 mb-2">{t('chickens')}</h2>
          <p className="text-neutral-600">{t('no_farm_selected')}</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-4xl font-bold text-neutral-900 font-display mb-2">
              🐔 {t('chickens')}
            </h2>
            <p className="text-lg text-neutral-600">
              Manage your poultry flock with detailed tracking
            </p>
          </div>
          {canManage && (
            <Button
              onClick={() => {
                setEditingChicken({});
                setIsModalOpen(true);
              }}
              size="lg"
              className="shadow-medium"
              data-testid="add-new-chicken-button"
            >
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              {t('add_new_chicken')}
            </Button>
          )}
        </div>

        {/* Status Overview Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-primary-600">{chickens.length}</div>
              <div className="text-sm text-neutral-600">Total Chickens</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{statusCounts.healthy || 0}</div>
              <div className="text-sm text-neutral-600">Healthy</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{statusCounts.laying || 0}</div>
              <div className="text-sm text-neutral-600">Laying</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{statusCounts.sick || 0}</div>
              <div className="text-sm text-neutral-600">Sick</div>
            </CardContent>
          </Card>
        </div>
      </div>

      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-red-700">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filter */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-neutral-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search chickens by breed, ID, or notes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 shadow-soft hover:shadow-medium"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-neutral-400 hover:text-neutral-600"
                >
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>

            {/* Filter Buttons */}
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-neutral-700 flex items-center mr-2">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                </svg>
                Filter:
              </span>
              {[
                { key: 'all', label: 'All', icon: '📋' },
                { key: 'healthy', label: 'Healthy', icon: '🟢' },
                { key: 'laying', label: 'Laying', icon: '🥚' },
                { key: 'sick', label: 'Sick', icon: '🔴' },
                { key: 'sold', label: 'Sold', icon: '💰' },
                { key: 'deceased', label: 'Deceased', icon: '⚰️' }
              ].map((filter) => (
                <Button
                  key={filter.key}
                  variant={filterStatus === filter.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterStatus(filter.key)}
                  className="transition-all duration-200 hover:scale-105"
                >
                  <span className="mr-1">{filter.icon}</span>
                  {filter.label}
                  {filter.key !== 'all' && statusCounts[filter.key] > 0 && (
                    <Badge variant="secondary" size="sm" className="ml-2">
                      {statusCounts[filter.key]}
                    </Badge>
                  )}
                  {filter.key === 'all' && (
                    <Badge variant="secondary" size="sm" className="ml-2">
                      {chickens.length}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>

            {/* Results Summary */}
            {(searchTerm || filterStatus !== 'all') && (
              <div className="flex items-center justify-between text-sm text-neutral-600 bg-neutral-50 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>
                    Showing {filteredChickens.length} of {chickens.length} chickens
                    {searchTerm && ` matching "${searchTerm}"`}
                    {filterStatus !== 'all' && ` with status "${filterStatus}"`}
                  </span>
                </div>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setFilterStatus('all');
                  }}
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  Clear filters
                </button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Chickens List */}
      {loading ? (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <SkeletonCard />
              </Card>
            ))}
          </div>
        </div>
      ) : (
        <ChickenList
          chickens={filteredChickens}
          onEdit={canManage ? (chicken) => {
            setEditingChicken(chicken);
            setIsModalOpen(true);
          } : null}
          onDelete={canManage ? handleDeleteChicken : null}
        />
      )}

      {/* Modal for Add/Edit Chicken */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingChicken(null);
        }}
        title={editingChicken?._id ? 'Edit Chicken' : 'Add New Chicken'}
        size="lg"
      >
        {editingChicken && (
          <ChickenForm
            initialData={editingChicken}
            onSubmit={async (data) => {
              if (editingChicken._id) {
                await handleUpdateChicken(editingChicken._id, data);
              } else {
                await handleAddChicken(data);
              }
              setIsModalOpen(false);
              setEditingChicken(null);
            }}
            onCancel={() => {
              setIsModalOpen(false);
              setEditingChicken(null);
            }}
          />
        )}
      </Modal>
    </DashboardLayout>
  );
};

export default ChickensPage;
