import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Input from './ui/Input';
import Select from './ui/Select';
import Button from './ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card';

const ChickenForm = ({ initialData = {}, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const [breed, setBreed] = useState(initialData.breed || '');
  const [hatchDate, setHatchDate] = useState(initialData.hatchDate ? new Date(initialData.hatchDate).toISOString().split('T')[0] : '');
  const [status, setStatus] = useState(initialData.status || 'healthy');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Validation states
  const [breedError, setBreedError] = useState(null);
  const [hatchDateError, setHatchDateError] = useState(null);

  useEffect(() => {
    if (initialData._id) {
      setBreed(initialData.breed);
      setHatchDate(initialData.hatchDate ? new Date(initialData.hatchDate).toISOString().split('T')[0] : '');
      setStatus(initialData.status);
    } else {
      // Reset form for new entry
      setBreed('');
      setHatchDate('');
      setStatus('healthy');
    }
    setError(null);
    setBreedError(null);
    setHatchDateError(null);
  }, [initialData]);

  const validateForm = () => {
    let isValid = true;
    if (!breed.trim()) {
      setBreedError(t('breed_required'));
      isValid = false;
    } else {
      setBreedError(null);
    }

    if (!hatchDate) {
      setHatchDateError(t('hatch_date_required'));
      isValid = false;
    } else {
      setHatchDateError(null);
    }
    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      await onSubmit({ breed, hatchDate, status });
    } catch (err) {
      setError(err.response?.data?.message || t('failed_to_save_chicken'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="animate-fade-in">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span className="text-2xl">🐔</span>
          <span>{initialData._id ? t('edit_chicken') : t('add_new_chicken')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <p className="text-red-700 font-medium">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label={t('breed')}
              value={breed}
              onChange={(e) => setBreed(e.target.value)}
              error={!!breedError}
              helperText={breedError}
              required
              placeholder="Enter chicken breed"
              icon={
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            />

            <Input
              type="date"
              label={t('hatch_date')}
              value={hatchDate}
              onChange={(e) => setHatchDate(e.target.value)}
              error={!!hatchDateError}
              helperText={hatchDateError}
              required
              icon={
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
              }
            />
          </div>

          <Select
            label={t('status')}
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            required
          >
            <option value="healthy">🟢 {t('healthy')}</option>
            <option value="laying">🥚 {t('laying')}</option>
            <option value="sick">🔴 {t('sick')}</option>
            <option value="sold">💰 {t('sold')}</option>
            <option value="deceased">⚰️ {t('deceased')}</option>
          </Select>

          <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-neutral-200">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="w-full sm:w-auto"
              disabled={isLoading}
            >
              {t('cancel')}
            </Button>
            <Button
              type="submit"
              loading={isLoading}
              className="w-full sm:w-auto"
            >
              {initialData._id ? t('update') : t('save')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ChickenForm;