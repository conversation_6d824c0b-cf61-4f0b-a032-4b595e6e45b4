import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent } from './ui/Card';
import Badge from './ui/Badge';

const SummaryCard = ({ title, value, unit, icon, gradient, badgeVariant, trend }) => {
  return (
    <Card className="group hover:scale-105 transition-all duration-300 cursor-pointer overflow-hidden">
      <div className={`h-2 w-full ${gradient}`}></div>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className={`p-3 rounded-xl ${gradient} bg-opacity-10`}>
            <div className="h-6 w-6 text-white">
              {icon}
            </div>
          </div>
          {trend && (
            <Badge variant={badgeVariant} size="sm">
              {trend}
            </Badge>
          )}
        </div>
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-neutral-600 uppercase tracking-wide">
            {title}
          </h3>
          <div className="flex items-baseline space-x-2">
            <p className="text-3xl font-bold text-neutral-900 font-display">
              {value?.toLocaleString() || 0}
            </p>
            {unit && (
              <span className="text-lg font-medium text-neutral-500">
                {unit}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const SummaryCards = ({ data }) => {
  const { t } = useTranslation();

  // Calculate trends (mock data for now)
  const trends = {
    eggs: '+12%',
    income: '+8%',
    expense: '-5%',
    chickens: '+2%'
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <SummaryCard
        title={t('total_eggs')}
        value={data.totalEggs || 0}
        unit=""
        icon={
          <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
            <path d="M12 2C8.5 2 6 6.5 6 12s2.5 10 6 10 6-4.5 6-10S15.5 2 12 2z"/>
          </svg>
        }
        gradient="bg-gradient-to-r from-blue-500 to-blue-600"
        badgeVariant="info"
        trend={trends.eggs}
      />
      <SummaryCard
        title={t('total_income')}
        value={data.totalIncome || 0}
        unit="NPR"
        icon={
          <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        }
        gradient="bg-gradient-to-r from-green-500 to-green-600"
        badgeVariant="success"
        trend={trends.income}
      />
      <SummaryCard
        title={t('total_expense')}
        value={data.totalExpense || 0}
        unit="NPR"
        icon={
          <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
            <path d="M7 18c-1.1 0-2-.9-2-2V8c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2H7zm0-10v8h10V8H7zm2 2h6v2H9v-2zm0 3h4v1H9v-1z"/>
          </svg>
        }
        gradient="bg-gradient-to-r from-red-500 to-red-600"
        badgeVariant="success"
        trend={trends.expense}
      />
      <SummaryCard
        title={t('total_chickens')}
        value={data.totalChickens || 0}
        unit=""
        icon={
          <svg viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
            <path d="M8.5 5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5S8.5 6.38 8.5 5zM12 8c-2.21 0-4 1.79-4 4v8h2v-8c0-1.1.9-2 2-2s2 .9 2 2v8h2v-8c0-2.21-1.79-4-4-4z"/>
          </svg>
        }
        gradient="bg-gradient-to-r from-yellow-500 to-yellow-600"
        badgeVariant="warning"
        trend={trends.chickens}
      />
    </div>
  );
};

export default SummaryCards;
