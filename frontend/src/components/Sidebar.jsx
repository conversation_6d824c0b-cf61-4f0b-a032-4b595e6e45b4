import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';

// Modern NavLink component
const NavLink = ({ to, icon, children, className = '' }) => {
  const location = useLocation();
  const isActive = location.pathname === to;

  return (
    <Link
      to={to}
      className={`
        group flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 ease-in-out
        ${isActive
          ? 'bg-primary-600 text-white shadow-medium transform scale-105'
          : 'text-neutral-300 hover:bg-neutral-700 hover:text-white hover:transform hover:scale-105'
        }
        ${className}
      `}
    >
      <span className="text-lg">{icon}</span>
      <span className="font-medium">{children}</span>
      {isActive && (
        <div className="ml-auto w-2 h-2 bg-white rounded-full animate-bounce-soft"></div>
      )}
    </Link>
  );
};

const Sidebar = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Mobile sidebar toggle */}
      <button
        className="md:hidden fixed top-4 left-4 z-50 p-3 rounded-lg bg-white shadow-large border border-neutral-200 text-neutral-700 hover:bg-neutral-50 transition-all duration-200"
        onClick={() => setIsOpen(!isOpen)}
        aria-label={isOpen ? 'Close menu' : 'Open menu'}
      >
        <svg
          className="w-5 h-5 transition-transform duration-200"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          style={{ transform: isOpen ? 'rotate(90deg)' : 'rotate(0deg)' }}
        >
          {isOpen ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          )}
        </svg>
      </button>

      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="md:hidden fixed inset-0 z-30 bg-black/50 backdrop-blur-sm animate-fade-in"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-40 w-64 bg-gradient-to-b from-neutral-900 via-neutral-800 to-neutral-900 text-white flex flex-col transform ${isOpen ? 'translate-x-0' : '-translate-x-full'} md:relative md:translate-x-0 transition-all duration-300 ease-in-out shadow-large`}
      >
        {/* Logo Section */}
        <div className="p-6 border-b border-neutral-700 bg-gradient-to-r from-primary-600 to-primary-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
              <svg viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-primary-600">
                <path d="M12 2C8.5 2 6 6.5 6 12s2.5 10 6 10 6-4.5 6-10S15.5 2 12 2z"/>
              </svg>
            </div>
            <h1 className="text-xl font-bold font-display">SmartPoultry</h1>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
          <NavLink to="/dashboard" icon="📊">
            {t('dashboard')}
          </NavLink>
          <NavLink to="/chickens" icon="🐔">
            {t('chickens')}
          </NavLink>
          <NavLink to="/egg-production" icon="🥚">
            {t('egg_production')}
          </NavLink>
          <NavLink to="/feed-records" icon="🌾">
            {t('feed_records')}
          </NavLink>
          <NavLink to="/health-records" icon="🏥">
            {t('health_records')}
          </NavLink>
          <NavLink to="/financial-records" icon="💰">
            {t('financial_records')}
          </NavLink>
          <NavLink to="/suppliers" icon="🏪">
            {t('suppliers')}
          </NavLink>
          <NavLink to="/customers" icon="👥">
            {t('customers')}
          </NavLink>
          <NavLink to="/ai-insights" icon="🤖">
            {t('ai_insight_history')}
          </NavLink>
          {user && user.roles.includes('admin') && (
            <NavLink to="/users" icon="👤">
              {t('user_management')}
            </NavLink>
          )}
        </nav>
      </div>
    </>
  );
};

export default Sidebar;