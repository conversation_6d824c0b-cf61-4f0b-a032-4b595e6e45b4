import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useSocket } from '../contexts/SocketContext';
import Button from './ui/Button';
import Badge from './ui/Badge';

const Navbar = () => {
  const { t, i18n } = useTranslation();
  const { user, logout } = useAuth();
  const { notifications, clearNotifications } = useSocket();
  const navigate = useNavigate();
  const [language, setLanguage] = useState(i18n.language);

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    setLanguage(lng);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <header className="bg-white/80 backdrop-blur-md shadow-soft border-b border-neutral-200 p-4">
      <div className="flex flex-col sm:flex-row justify-between items-center">
        {/* Page Title */}
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl font-bold text-neutral-900 font-display">
            {t('dashboard')}
          </h1>
          <p className="text-sm text-neutral-600 mt-1">
            Welcome back, {user?.name || 'User'}!
          </p>
        </div>

        {/* Right Section */}
        <div className="flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4">

          {/* Notifications */}
          {notifications.length > 0 && (
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Button
                  variant="warning"
                  size="sm"
                  onClick={() => alert(notifications.map(n => n.message).join('\n'))}
                  className="relative"
                >
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2C6.477 2 3.5 4.977 3.5 8.5c0 2.5 1.5 4.5 1.5 6.5h10c0-2 1.5-4 1.5-6.5C16.5 4.977 13.523 2 10 2z"/>
                  </svg>
                  {t('notifications')}
                  <Badge variant="danger" size="sm" className="ml-2">
                    {notifications.length}
                  </Badge>
                </Button>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearNotifications}
              >
                Clear
              </Button>
            </div>
          )}

          {/* Language Switcher */}
          <div className="flex items-center bg-neutral-100 rounded-lg p-1">
            <button
              onClick={() => changeLanguage('en')}
              className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                language === 'en'
                  ? 'bg-white text-primary-600 shadow-soft'
                  : 'text-neutral-600 hover:text-neutral-900'
              }`}
            >
              EN
            </button>
            <button
              onClick={() => changeLanguage('ne')}
              className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                language === 'ne'
                  ? 'bg-white text-primary-600 shadow-soft'
                  : 'text-neutral-600 hover:text-neutral-900'
              }`}
            >
              नेपाली
            </button>
          </div>

          {/* User Menu */}
          {user && (
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold text-sm">
                    {user.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-neutral-900">{user.name}</p>
                  <p className="text-xs text-neutral-500">{user.email}</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="border-red-300 text-red-600 hover:bg-red-50"
              >
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
                </svg>
                {t('logout')}
              </Button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Navbar;
