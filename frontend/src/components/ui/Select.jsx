import React from 'react';
import { cn } from '../../utils/cn';

const Select = React.forwardRef(({
  className,
  error = false,
  label,
  helperText,
  required = false,
  children,
  ...props
}, ref) => {
  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <div className="relative">
        <select
          className={cn(
            'block w-full rounded-lg border border-neutral-300 bg-white px-3 py-2.5',
            'text-neutral-900',
            'focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20',
            'transition-all duration-200 ease-in-out',
            'shadow-soft hover:shadow-medium focus:shadow-medium',
            'disabled:bg-neutral-50 disabled:text-neutral-500 disabled:cursor-not-allowed',
            'hover:border-neutral-400',
            'appearance-none cursor-pointer',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50',
            className
          )}
          ref={ref}
          {...props}
        >
          {children}
        </select>
        {/* Custom dropdown arrow */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg className="w-5 h-5 text-neutral-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
      {helperText && (
        <p className={cn(
          'mt-2 text-sm flex items-center',
          error ? 'text-red-600' : 'text-neutral-600'
        )}>
          {error && (
            <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          )}
          {helperText}
        </p>
      )}
    </div>
  );
});

Select.displayName = 'Select';

export default Select;
