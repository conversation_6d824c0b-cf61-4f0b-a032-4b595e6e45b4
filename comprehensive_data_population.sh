#!/bin/bash

# Comprehensive SmartPoultry Data Population Script
# This script populates ALL entity types with realistic data

BASE_URL="http://localhost:5001/api/v1"
MANAGER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTE5M2M1MDI0OWUyNWQ4NzYyY2Y1NyIsImlhdCI6MTc1NDM3MTAxNCwiZXhwIjoxNzU0Mzc0NjE0fQ._QoVF9Laz0vXnRpjUlQ5jEnhkoVCGAvXEKZA20mKKTY"
ADMIN_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTE5NmMyOTFiMmUxYjk0MzdiODE0YSIsImlhdCI6MTc1NDM3MTc3OCwiZXhwIjoxNzU0Mzc1Mzc4fQ.GfdRiIyiFJx2A_5yxh_ZjF4UwsnWNdtlZnXeDv4-rLc"
FARM_ID="689193c60249e25d8762cf59"
ADMIN_FARM_ID="689196c291b2e1b9437b814c"

echo "🚀 COMPREHENSIVE SMARTPOULTRY DATA POPULATION"
echo "=============================================="

# Function to get date N days ago (macOS compatible)
get_date_days_ago() {
    date -v-${1}d +%Y-%m-%d
}

# Function to generate random number between min and max
random_between() {
    echo $(( RANDOM % ($2 - $1 + 1) + $1 ))
}

echo ""
echo "📦 1. POPULATING CHICKENS (50 chickens with diverse data)"
echo "--------------------------------------------------------"

breeds=("Rhode Island Red" "Leghorn" "Sussex" "Orpington" "Australorp" "Brahma" "Wyandotte" "Plymouth Rock" "Buff Orpington" "New Hampshire")
statuses=("healthy" "sick" "laying" "sold" "deceased")
chicken_ids=()

for i in {1..50}; do
    breed_index=$(random_between 0 9)
    breed=${breeds[$breed_index]}
    
    status_index=$(random_between 0 4)
    status=${statuses[$status_index]}
    
    # Adjust status probabilities (80% healthy, 10% laying, 5% sick, 3% sold, 2% deceased)
    rand=$(random_between 1 100)
    if [ $rand -le 80 ]; then
        status="healthy"
    elif [ $rand -le 90 ]; then
        status="laying"
    elif [ $rand -le 95 ]; then
        status="sick"
    elif [ $rand -le 98 ]; then
        status="sold"
    else
        status="deceased"
    fi
    
    days_old=$(random_between 30 730) # 1 month to 2 years old
    hatch_date=$(get_date_days_ago $days_old)
    
    response=$(curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/chickens" \
        -H "Authorization: Bearer ${MANAGER_TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"breed\": \"${breed}\",
            \"hatchDate\": \"${hatch_date}\",
            \"status\": \"${status}\",
            \"notes\": \"Chicken #${i} - ${breed} breed, hatched ${hatch_date}\"
        }")
    
    # Extract chicken ID for health records
    chicken_id=$(echo $response | grep -o '"_id":"[^"]*"' | cut -d'"' -f4)
    if [ ! -z "$chicken_id" ]; then
        chicken_ids+=($chicken_id)
    fi
    
    if [ $((i % 10)) -eq 0 ]; then
        echo "✅ Created $i/50 chickens (Latest: ${breed} - ${status})"
    fi
done

echo ""
echo "🥚 2. POPULATING EGG PRODUCTION (180 days of data)"
echo "------------------------------------------------"

for days in {180..0}; do
    date=$(get_date_days_ago $days)
    
    # Realistic egg production with seasonal variations
    base_production=42
    seasonal_factor=$(echo "scale=2; s(($days * 3.14159 / 182.5)) * 8" | bc -l 2>/dev/null || echo "0")
    seasonal_int=$(printf "%.0f" "$seasonal_factor" 2>/dev/null || echo "0")
    daily_variation=$(random_between -12 12)
    
    quantity=$((base_production + seasonal_int + daily_variation))
    if [ $quantity -lt 0 ]; then
        quantity=0
    fi
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/egg-production" \
        -H "Authorization: Bearer ${MANAGER_TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"quantity\": ${quantity},
            \"notes\": \"Daily collection - ${quantity} eggs collected\"
        }" > /dev/null
    
    if [ $((days % 30)) -eq 0 ]; then
        echo "✅ Created egg production for ${date} (${quantity} eggs)"
    fi
done

echo ""
echo "🌾 3. POPULATING FEED RECORDS (26 weeks of data)"
echo "----------------------------------------------"

feed_types=("Layer Feed" "Starter Feed" "Grower Feed" "Organic Layer Feed" "Finisher Feed" "Breeder Feed")
suppliers=("AgriCorp Nepal" "Poultry Plus Pvt Ltd" "FeedMaster Nepal" "Himalayan Feeds" "Valley Feed Store")

for weeks in {26..0}; do
    days=$((weeks * 7))
    date=$(get_date_days_ago $days)
    
    feed_index=$(random_between 0 5)
    feed_type=${feed_types[$feed_index]}
    
    supplier_index=$(random_between 0 4)
    supplier=${suppliers[$supplier_index]}
    
    quantity=$(random_between 45 75) # kg per week
    cost_per_kg=$(random_between 50 80) # NPR per kg
    total_cost=$((quantity * cost_per_kg))
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/feed-records" \
        -H "Authorization: Bearer ${MANAGER_TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"feedType\": \"${feed_type}\",
            \"quantity\": ${quantity},
            \"cost\": ${total_cost},
            \"supplier\": \"${supplier}\",
            \"notes\": \"Weekly feed purchase - ${quantity}kg ${feed_type} from ${supplier}\"
        }" > /dev/null
    
    if [ $((weeks % 4)) -eq 0 ]; then
        echo "✅ Created feed record for ${date} (${quantity}kg ${feed_type} - ${total_cost} NPR)"
    fi
done

echo ""
echo "🏥 4. POPULATING HEALTH RECORDS (50 records)"
echo "------------------------------------------"

health_types=("vaccination" "treatment" "checkup" "medication")
treatments=("Newcastle Vaccine" "Fowl Pox Vaccine" "Deworming" "Vitamin B Complex" "Antibiotic Treatment" "Calcium Supplement" "Iron Supplement" "Respiratory Treatment" "Digestive Treatment" "Preventive Care")

for i in {1..50}; do
    days=$(random_between 1 180)
    date=$(get_date_days_ago $days)
    
    # Select random chicken (if we have any)
    if [ ${#chicken_ids[@]} -gt 0 ]; then
        chicken_index=$(random_between 0 $((${#chicken_ids[@]} - 1)))
        chicken_id=${chicken_ids[$chicken_index]}
    else
        chicken_id=""
    fi
    
    type_index=$(random_between 0 3)
    type=${health_types[$type_index]}
    
    treatment_index=$(random_between 0 9)
    treatment=${treatments[$treatment_index]}
    
    cost=$(random_between 100 1500) # NPR
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/health-records" \
        -H "Authorization: Bearer ${MANAGER_TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"chickenId\": \"${chicken_id}\",
            \"type\": \"${type}\",
            \"description\": \"${treatment}\",
            \"treatment\": \"${treatment}\",
            \"cost\": ${cost},
            \"notes\": \"${type} - ${treatment} administered\"
        }" > /dev/null
    
    if [ $((i % 10)) -eq 0 ]; then
        echo "✅ Created health record $i/50 (${treatment} - ${cost} NPR)"
    fi
done

echo ""
echo "💰 5. POPULATING FINANCIAL RECORDS (100 records)"
echo "-----------------------------------------------"

# Income records (egg sales, chicken sales)
income_categories=("Egg Sales" "Chicken Sales" "Manure Sales" "Consultation Fee")
for i in {1..60}; do
    days=$(random_between 1 180)
    date=$(get_date_days_ago $days)
    
    cat_index=$(random_between 0 3)
    category=${income_categories[$cat_index]}
    
    case $category in
        "Egg Sales")
            amount=$(random_between 800 2500)
            description="Daily egg sales to local market"
            ;;
        "Chicken Sales")
            amount=$(random_between 1500 8000)
            description="Live chicken sales"
            ;;
        "Manure Sales")
            amount=$(random_between 500 1200)
            description="Organic manure sales to farmers"
            ;;
        "Consultation Fee")
            amount=$(random_between 1000 3000)
            description="Farm consultation services"
            ;;
    esac
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/financial-records" \
        -H "Authorization: Bearer ${MANAGER_TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"type\": \"income\",
            \"category\": \"${category}\",
            \"amount\": ${amount},
            \"description\": \"${description}\",
            \"notes\": \"${category} - ${amount} NPR\"
        }" > /dev/null
    
    if [ $((i % 15)) -eq 0 ]; then
        echo "✅ Created income record $i/60 (${category}: ${amount} NPR)"
    fi
done

# Expense records
expense_categories=("Feed" "Medicine" "Equipment" "Labor" "Utilities" "Transportation" "Maintenance" "Insurance" "Veterinary" "Supplies")
for i in {1..40}; do
    days=$(random_between 1 180)
    date=$(get_date_days_ago $days)
    
    cat_index=$(random_between 0 9)
    category=${expense_categories[$cat_index]}
    
    case $category in
        "Feed")
            amount=$(random_between 2000 6000)
            description="Feed purchase for chickens"
            ;;
        "Medicine")
            amount=$(random_between 300 2000)
            description="Veterinary medicines and vaccines"
            ;;
        "Equipment")
            amount=$(random_between 1000 15000)
            description="Farm equipment and tools"
            ;;
        "Labor")
            amount=$(random_between 2000 8000)
            description="Farm worker wages"
            ;;
        "Utilities")
            amount=$(random_between 500 2000)
            description="Electricity and water bills"
            ;;
        "Transportation")
            amount=$(random_between 300 1500)
            description="Transportation costs"
            ;;
        "Maintenance")
            amount=$(random_between 800 3000)
            description="Farm maintenance and repairs"
            ;;
        "Insurance")
            amount=$(random_between 1000 5000)
            description="Farm and livestock insurance"
            ;;
        "Veterinary")
            amount=$(random_between 500 3000)
            description="Veterinary consultation fees"
            ;;
        "Supplies")
            amount=$(random_between 200 1000)
            description="General farm supplies"
            ;;
    esac
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/financial-records" \
        -H "Authorization: Bearer ${MANAGER_TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"type\": \"expense\",
            \"category\": \"${category}\",
            \"amount\": ${amount},
            \"description\": \"${description}\",
            \"notes\": \"${category} expense - ${amount} NPR\"
        }" > /dev/null
    
    if [ $((i % 10)) -eq 0 ]; then
        echo "✅ Created expense record $i/40 (${category}: ${amount} NPR)"
    fi
done

echo ""
echo "🏪 6. POPULATING SUPPLIERS (15 suppliers)"
echo "---------------------------------------"

supplier_names=("AgriCorp Nepal Pvt Ltd" "Poultry Plus Suppliers" "FeedMaster Nepal" "Himalayan Feeds Co" "Valley Feed Store" "Nepal Veterinary Supplies" "Kathmandu Feed Center" "Organic Farm Supplies" "Modern Poultry Equipment" "Rural Development Feeds" "Everest Animal Health" "Chitwan Feed Mills" "Bhaktapur Agro Center" "Lalitpur Poultry Supplies" "Janakpur Feed House")
contact_persons=("Ram Sharma" "Sita Poudel" "Krishna Thapa" "Maya Gurung" "Bikash Shrestha" "Sunita Rai" "Deepak Tamang" "Kamala Magar" "Rajesh Adhikari" "Parvati Limbu" "Gopal Neupane" "Radha Karki" "Suresh Bhattarai" "Gita Chaudhary" "Mohan Khadka")

for i in {0..14}; do
    supplier_name=${supplier_names[$i]}
    contact_person=${contact_persons[$i]}
    
    phone="98$(random_between 10000000 99999999)"
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/suppliers" \
        -H "Authorization: Bearer ${MANAGER_TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"${supplier_name}\",
            \"contactPerson\": \"${contact_person}\",
            \"phone\": \"${phone}\",
            \"email\": \"$(echo ${contact_person,,} | tr ' ' '.')@${supplier_name,,}.com\",
            \"address\": \"Kathmandu, Nepal\",
            \"notes\": \"Reliable supplier for poultry needs\",
            \"products\": [\"Feed\", \"Medicine\", \"Equipment\"]
        }" > /dev/null
    
    echo "✅ Created supplier: ${supplier_name} (Contact: ${contact_person})"
done

echo ""
echo "👥 7. POPULATING CUSTOMERS (20 customers)"
echo "---------------------------------------"

customer_names=("Sunrise Hotel Kathmandu" "Mountain View Restaurant" "Local Market Vendors" "Organic Food Store" "Fresh Mart Supermarket" "Hotel Himalaya" "Green Valley Restaurant" "City Bakery & Cafe" "Healthy Living Store" "Farm Fresh Outlets" "Golden Gate Hotel" "Everest View Lodge" "Kathmandu Guest House" "Thamel Organic Shop" "Patan Durbar Restaurant" "Bhaktapur Heritage Hotel" "Pokhara Lake Resort" "Chitwan Jungle Lodge" "Lumbini Garden Hotel" "Mustang Eco Resort")
contact_persons=("Ramesh Maharjan" "Sushila Shakya" "Binod Manandhar" "Kamala Joshi" "Prakash Amatya" "Sunita Bajracharya" "Deepak Tuladhar" "Gita Pradhan" "Sunil Dangol" "Purnima Chitrakar" "Rajesh Nakarmi" "Sabita Kaji" "Mohan Lal Shrestha" "Indira Malla" "Bishnu Sthapit" "Laxmi Ranjitkar" "Hari Tamrakar" "Sarita Maharjan" "Govinda Awale" "Kamala Rajbhandari")

for i in {0..19}; do
    customer_name=${customer_names[$i]}
    contact_person=${contact_persons[$i]}
    
    phone="98$(random_between 10000000 99999999)"
    
    # Generate order history
    order_history="["
    for j in {1..3}; do
        order_days=$(random_between 10 90)
        order_date=$(get_date_days_ago $order_days)
        quantity=$(random_between 50 300)
        amount=$(random_between 500 3000)
        
        if [ $j -gt 1 ]; then
            order_history+=","
        fi
        order_history+="{\"date\":\"${order_date}\",\"item\":\"Fresh Eggs\",\"quantity\":${quantity},\"amount\":${amount}}"
    done
    order_history+="]"
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/customers" \
        -H "Authorization: Bearer ${MANAGER_TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"${customer_name}\",
            \"contactPerson\": \"${contact_person}\",
            \"phone\": \"${phone}\",
            \"email\": \"$(echo ${contact_person,,} | tr ' ' '.')@${customer_name,,}.com\",
            \"address\": \"Kathmandu Valley, Nepal\",
            \"notes\": \"Regular customer for fresh eggs\",
            \"orderHistory\": ${order_history}
        }" > /dev/null
    
    echo "✅ Created customer: ${customer_name} (Contact: ${contact_person})"
done

echo ""
echo "🎉 DATA POPULATION COMPLETED SUCCESSFULLY!"
echo "========================================"
echo ""
echo "📊 SUMMARY OF POPULATED DATA:"
echo "- 50 Chickens (diverse breeds and statuses)"
echo "- 181 Egg Production records (6 months)"
echo "- 27 Feed Records (26 weeks)"
echo "- 50 Health Records (treatments, vaccinations)"
echo "- 100 Financial Records (60 income + 40 expense)"
echo "- 15 Suppliers (complete contact info)"
echo "- 20 Customers (with order history)"
echo ""
echo "🔑 TOTAL RECORDS: ~443 realistic farm management records"
echo ""
echo "✅ All CRUD operations should now work with manager role!"
echo "✅ Ready for comprehensive testing and UI enhancements!"
