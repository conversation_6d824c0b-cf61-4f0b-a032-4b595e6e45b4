#!/bin/bash

BASE_URL="http://localhost:5001/api/v1"
MANAGER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTE5M2M1MDI0OWUyNWQ4NzYyY2Y1NyIsImlhdCI6MTc1NDM3MTAxNCwiZXhwIjoxNzU0Mzc0NjE0fQ._QoVF9Laz0vXnRpjUlQ5jEnhkoVCGAvXEKZA20mKKTY"
FARM_ID="689193c60249e25d8762cf59"

echo "🏪 CREATING SUPPLIERS"
echo "==================="

# Create suppliers
curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/suppliers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "AgriCorp Nepal Pvt Ltd",
        "contactPerson": "<PERSON>",
        "phone": "9841234567",
        "email": "<EMAIL>",
        "address": "Kathmandu, Nepal",
        "notes": "Premium feed supplier",
        "products": ["Layer Feed", "Starter Feed", "Medicine"]
    }' > /dev/null && echo "✅ Created AgriCorp Nepal"

curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/suppliers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Poultry Plus Suppliers",
        "contactPerson": "Sita Poudel",
        "phone": "9851234567",
        "email": "<EMAIL>",
        "address": "Lalitpur, Nepal",
        "notes": "Equipment and medicine supplier",
        "products": ["Equipment", "Medicine", "Supplements"]
    }' > /dev/null && echo "✅ Created Poultry Plus"

curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/suppliers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "FeedMaster Nepal",
        "contactPerson": "Krishna Thapa",
        "phone": "9861234567",
        "email": "<EMAIL>",
        "address": "Bhaktapur, Nepal",
        "notes": "Organic feed specialist",
        "products": ["Organic Feed", "Grower Feed", "Finisher Feed"]
    }' > /dev/null && echo "✅ Created FeedMaster Nepal"

curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/suppliers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Himalayan Feeds Co",
        "contactPerson": "Maya Gurung",
        "phone": "9871234567",
        "email": "<EMAIL>",
        "address": "Pokhara, Nepal",
        "notes": "Quality feed manufacturer",
        "products": ["Layer Feed", "Breeder Feed", "Supplements"]
    }' > /dev/null && echo "✅ Created Himalayan Feeds"

curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/suppliers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Valley Feed Store",
        "contactPerson": "Bikash Shrestha",
        "phone": "9881234567",
        "email": "<EMAIL>",
        "address": "Kathmandu Valley, Nepal",
        "notes": "Local feed distributor",
        "products": ["All Types of Feed", "Medicine", "Equipment"]
    }' > /dev/null && echo "✅ Created Valley Feed Store"

echo ""
echo "👥 CREATING CUSTOMERS"
echo "==================="

# Create customers
curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/customers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Sunrise Hotel Kathmandu",
        "contactPerson": "Ramesh Maharjan",
        "phone": "9841111111",
        "email": "<EMAIL>",
        "address": "Thamel, Kathmandu",
        "notes": "Regular bulk buyer - 200 eggs weekly",
        "orderHistory": [
            {"date": "2025-07-25", "item": "Fresh Eggs", "quantity": 200, "amount": 2000},
            {"date": "2025-07-18", "item": "Fresh Eggs", "quantity": 200, "amount": 2000},
            {"date": "2025-07-11", "item": "Fresh Eggs", "quantity": 200, "amount": 2000}
        ]
    }' > /dev/null && echo "✅ Created Sunrise Hotel"

curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/customers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Mountain View Restaurant",
        "contactPerson": "Sushila Shakya",
        "phone": "9851111111",
        "email": "<EMAIL>",
        "address": "Durbarmarg, Kathmandu",
        "notes": "Premium restaurant client",
        "orderHistory": [
            {"date": "2025-07-28", "item": "Fresh Eggs", "quantity": 150, "amount": 1650},
            {"date": "2025-07-21", "item": "Fresh Eggs", "quantity": 150, "amount": 1650},
            {"date": "2025-07-14", "item": "Fresh Eggs", "quantity": 150, "amount": 1650}
        ]
    }' > /dev/null && echo "✅ Created Mountain View Restaurant"

curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/customers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Fresh Mart Supermarket",
        "contactPerson": "Binod Manandhar",
        "phone": "9861111111",
        "email": "<EMAIL>",
        "address": "New Road, Kathmandu",
        "notes": "Supermarket chain - high volume",
        "orderHistory": [
            {"date": "2025-08-01", "item": "Fresh Eggs", "quantity": 500, "amount": 5500},
            {"date": "2025-07-25", "item": "Fresh Eggs", "quantity": 500, "amount": 5500},
            {"date": "2025-07-18", "item": "Fresh Eggs", "quantity": 500, "amount": 5500}
        ]
    }' > /dev/null && echo "✅ Created Fresh Mart Supermarket"

curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/customers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Organic Food Store",
        "contactPerson": "Kamala Joshi",
        "phone": "9871111111",
        "email": "<EMAIL>",
        "address": "Patan, Lalitpur",
        "notes": "Organic products specialist",
        "orderHistory": [
            {"date": "2025-07-30", "item": "Organic Eggs", "quantity": 100, "amount": 1200},
            {"date": "2025-07-23", "item": "Organic Eggs", "quantity": 100, "amount": 1200},
            {"date": "2025-07-16", "item": "Organic Eggs", "quantity": 100, "amount": 1200}
        ]
    }' > /dev/null && echo "✅ Created Organic Food Store"

curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/customers" \
    -H "Authorization: Bearer ${MANAGER_TOKEN}" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Hotel Himalaya",
        "contactPerson": "Prakash Amatya",
        "phone": "9881111111",
        "email": "<EMAIL>",
        "address": "Kupondole, Lalitpur",
        "notes": "5-star hotel client",
        "orderHistory": [
            {"date": "2025-08-02", "item": "Premium Eggs", "quantity": 300, "amount": 3600},
            {"date": "2025-07-26", "item": "Premium Eggs", "quantity": 300, "amount": 3600},
            {"date": "2025-07-19", "item": "Premium Eggs", "quantity": 300, "amount": 3600}
        ]
    }' > /dev/null && echo "✅ Created Hotel Himalaya"

echo ""
echo "✅ SUPPLIERS AND CUSTOMERS CREATED SUCCESSFULLY!"
echo "================================================"
